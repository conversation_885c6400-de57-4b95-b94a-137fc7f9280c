<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>eComQnA - AI Customer Support for E-commerce</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            colors: {
              'primary': '#4f46e5',
              'primary-hover': '#6366f1',
              'accent-pink': '#DB2777',
              'accent-cyan': '#22D3EE',

              // Light Theme
              'background-light': '#f8fafc',
              'surface-light': '#ffffff',
              'panel-light': '#ffffff',
              'secondary-light': '#f1f5f9',
              'text-light': '#0f172a',
              'text-secondary-light': '#64748b',
              'border-light': '#e2e8f0',
              'border-glass-light': 'rgba(0, 0, 0, 0.08)',
              
              // Dark Theme
              'background-dark': '#0D1117',
              'surface-dark': '#161B22',
              'panel-dark': '#1e293b',
              'text-dark': '#E6EDF3',
              'text-secondary-dark': '#8B949E',
              'border-dark': '#334155',
              'border-glass-dark': 'rgba(255, 255, 255, 0.1)',

              // Old names for compatibility
              'text-primary': '#E6EDF3', // text-dark
              'text-secondary': '#8B949E', // text-secondary-dark
              'text-inverted': '#e2e8f0',
              'secondary': '#f0f2f5', 
              'panel': '#ffffff',
            },
            animation: {
              'pulse-fast': 'pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              'aurora': 'aurora 60s infinite linear',
              'shimmer': 'shimmer 1.5s infinite',
              'fade-in-up': 'fade-in-up 0.8s ease-out',
            },
            keyframes: {
              'fade-in-up': {
                'from': {
                  opacity: '0',
                  transform: 'translateY(10px)'
                },
                'to': {
                  opacity: '1',
                  transform: 'translateY(0)'
                },
              },
              aurora: {
                '0%': { backgroundPosition: '0% 50%' },
                '50%': { backgroundPosition: '100% 50%' },
                '100%': { backgroundPosition: '0% 50%' },
              },
              shimmer: {
                '0%': { backgroundPosition: '200% 0' },
                '100%': { backgroundPosition: '-200% 0' },
              },
            }
          }
        }
      }
    </script>
    <style>
      body {
        font-family: 'Inter', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      /* Custom scrollbar for a modern look */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      ::-webkit-scrollbar-track {
        background: transparent;
      }
      ::-webkit-scrollbar-thumb {
        background: #cbd5e1; /* slate-300 */
        border-radius: 3px;
      }
      .dark ::-webkit-scrollbar-thumb {
        background: #475569; /* slate-600 */
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #94a3b8; /* slate-400 */
      }
      .dark ::-webkit-scrollbar-thumb:hover {
        background: #64748b; /* slate-500 */
      }

      /* Base styles for the aurora background effect */
      .aurora-background {
        position: relative;
        overflow: hidden;
      }
      .aurora-background::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 150%;
        padding-bottom: 150%;
        border-radius: 50%;
        background: radial-gradient(circle at center, rgba(219, 39, 119, 0.05) 0%, rgba(34, 211, 238, 0.05) 50%, transparent 70%);
        transform: translate(-50%, -50%);
        animation: aurora 60s linear infinite;
        will-change: transform;
        filter: blur(80px);
      }
      .grid-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: linear-gradient(rgba(148, 163, 184, 0.08) 1px, transparent 1px), linear-gradient(90deg, rgba(148, 163, 184, 0.08) 1px, transparent 1px);
        background-size: 40px 40px;
        opacity: 0.5;
        z-index: 0;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react": "https://aistudiocdn.com/react@^19.1.1",
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
    "react/": "https://aistudiocdn.com/react@^19.1.1/",
    "@google/genai": "https://aistudiocdn.com/@google/genai@^1.17.0",
    "marked": "https://aistudiocdn.com/marked@^16.2.1",
    "firebase/app": "https://www.gstatic.com/firebasejs/10.12.2/firebase-app.js",
    "firebase/auth": "https://www.gstatic.com/firebasejs/10.12.2/firebase-auth.js",
    "firebase/": "https://aistudiocdn.com/firebase@^12.2.1/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-background-light dark:bg-background-dark">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>