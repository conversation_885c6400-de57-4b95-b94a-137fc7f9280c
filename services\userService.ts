import { User as FirebaseUser } from 'firebase/auth';
import { User } from '../types';
import { createUser, getUser, updateUser } from './firestoreService';

export const ensureUserDocument = async (firebaseUser: FirebaseUser): Promise<User> => {
  try {
    // Check if user document already exists
    let user = await getUser(firebaseUser.uid);
    
    if (!user) {
      // Create new user document
      const now = new Date();
      const userData: Omit<User, 'id'> = {
        email: firebaseUser.email || '',
        displayName: firebaseUser.displayName || undefined,
        createdAt: now,
        updatedAt: now,
      };
      
      await createUser(firebaseUser.uid, userData);
      
      // Fetch the created user
      user = await getUser(firebaseUser.uid);
      if (!user) {
        throw new Error('Failed to create user document');
      }
    } else {
      // Update user info if it has changed
      const updates: Partial<Omit<User, 'id' | 'createdAt'>> = {};
      let hasUpdates = false;
      
      if (user.email !== firebaseUser.email && firebaseUser.email) {
        updates.email = firebaseUser.email;
        hasUpdates = true;
      }
      
      if (user.displayName !== firebaseUser.displayName) {
        updates.displayName = firebaseUser.displayName || undefined;
        hasUpdates = true;
      }
      
      if (hasUpdates) {
        await updateUser(firebaseUser.uid, updates);
        // Fetch updated user
        user = await getUser(firebaseUser.uid);
        if (!user) {
          throw new Error('Failed to fetch updated user');
        }
      }
    }
    
    return user;
  } catch (error) {
    console.error('Error ensuring user document:', error);
    throw new Error('Failed to manage user document');
  }
};

export const updateUserProfile = async (
  userId: string, 
  updates: { displayName?: string }
): Promise<void> => {
  try {
    await updateUser(userId, updates);
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw new Error('Failed to update user profile');
  }
};
