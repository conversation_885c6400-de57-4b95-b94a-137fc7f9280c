import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { User, Project, KnowledgeChunk, ProjectStatus, SourceType } from '../types';

// Collection names
const COLLECTIONS = {
  USERS: 'users',
  PROJECTS: 'projects',
  CHUNKS: 'chunks',
} as const;

// Helper function to convert Firestore timestamps to Date objects
const convertTimestamps = (data: any): any => {
  const converted = { ...data };
  Object.keys(converted).forEach(key => {
    if (converted[key] instanceof Timestamp) {
      converted[key] = converted[key].toDate();
    } else if (converted[key] && typeof converted[key] === 'object') {
      converted[key] = convertTimestamps(converted[key]);
    }
  });
  return converted;
};

// Helper function to sanitize data for Firestore (removes undefined values)
const sanitizeForFirestore = (data: any): any => {
  const sanitized: any = {};
  Object.keys(data).forEach(key => {
    const value = data[key];
    if (value !== undefined) {
      if (value && typeof value === 'object' && !(value instanceof Date) && !(value instanceof Timestamp)) {
        sanitized[key] = sanitizeForFirestore(value);
      } else {
        sanitized[key] = value;
      }
    }
    // Note: undefined values are simply omitted from the sanitized object
  });
  return sanitized;
};

// User operations
export const createUser = async (userId: string, userData: Omit<User, 'id'>): Promise<void> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const userWithTimestamps = {
      ...userData,
      createdAt: Timestamp.fromDate(userData.createdAt),
      updatedAt: Timestamp.fromDate(userData.updatedAt),
    };

    // Sanitize data to remove undefined values before sending to Firestore
    const sanitizedData = sanitizeForFirestore(userWithTimestamps);
    await setDoc(userRef, sanitizedData);
  } catch (error: any) {
    console.error('Error creating user:', error);

    // Provide more specific error messages
    if (error?.code === 'permission-denied') {
      throw new Error('Missing or insufficient permissions to create user document. Please check Firestore security rules.');
    } else if (error?.code === 'unavailable') {
      throw new Error('Firestore service is temporarily unavailable. Please try again later.');
    } else if (error?.code === 'unauthenticated') {
      throw new Error('User is not authenticated. Please sign in again.');
    } else {
      throw new Error(`Failed to create user: ${error?.message || 'Unknown error'}`);
    }
  }
};

export const getUser = async (userId: string): Promise<User | null> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const userSnap = await getDoc(userRef);

    if (userSnap.exists()) {
      const userData = userSnap.data();
      return convertTimestamps({ id: userSnap.id, ...userData }) as User;
    }
    return null;
  } catch (error: any) {
    console.error('Error getting user:', error);

    // Provide more specific error messages
    if (error?.code === 'permission-denied') {
      throw new Error('Missing or insufficient permissions to access user data. Please check Firestore security rules.');
    } else if (error?.code === 'unavailable') {
      throw new Error('Firestore service is temporarily unavailable. Please try again later.');
    } else if (error?.code === 'unauthenticated') {
      throw new Error('User is not authenticated. Please sign in again.');
    } else {
      throw new Error(`Failed to get user: ${error?.message || 'Unknown error'}`);
    }
  }
};

export const updateUser = async (userId: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): Promise<void> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now(),
    };

    // Sanitize data to remove undefined values before sending to Firestore
    const sanitizedData = sanitizeForFirestore(updateData);
    await updateDoc(userRef, sanitizedData);
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error('Failed to update user');
  }
};

// Project operations
export const createProject = async (projectData: Omit<Project, 'id'>): Promise<string> => {
  try {
    const projectsRef = collection(db, COLLECTIONS.PROJECTS);
    const projectWithTimestamps = {
      ...projectData,
      createdAt: Timestamp.fromDate(projectData.createdAt),
      updatedAt: Timestamp.fromDate(projectData.updatedAt),
      knowledgeBaseStats: {
        ...projectData.knowledgeBaseStats,
        lastTrainingDate: projectData.knowledgeBaseStats.lastTrainingDate 
          ? Timestamp.fromDate(projectData.knowledgeBaseStats.lastTrainingDate)
          : null,
      },
    };
    const docRef = await addDoc(projectsRef, projectWithTimestamps);
    return docRef.id;
  } catch (error) {
    console.error('Error creating project:', error);
    throw new Error('Failed to create project');
  }
};

export const getProject = async (projectId: string): Promise<Project | null> => {
  try {
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    const projectSnap = await getDoc(projectRef);
    
    if (projectSnap.exists()) {
      const projectData = projectSnap.data();
      return convertTimestamps({ id: projectSnap.id, ...projectData }) as Project;
    }
    return null;
  } catch (error) {
    console.error('Error getting project:', error);
    throw new Error('Failed to get project');
  }
};

export const getUserProjects = async (userId: string): Promise<Project[]> => {
  try {
    const projectsRef = collection(db, COLLECTIONS.PROJECTS);
    const q = query(
      projectsRef,
      where('ownerId', '==', userId),
      orderBy('updatedAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as Project
    );
  } catch (error) {
    console.error('Error getting user projects:', error);
    throw new Error('Failed to get user projects');
  }
};

export const updateProject = async (projectId: string, updates: Partial<Omit<Project, 'id' | 'createdAt'>>): Promise<void> => {
  try {
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now(),
    };
    
    // Handle nested timestamp conversion for knowledgeBaseStats
    if (updates.knowledgeBaseStats?.lastTrainingDate) {
      updateData.knowledgeBaseStats = {
        ...updates.knowledgeBaseStats,
        lastTrainingDate: Timestamp.fromDate(updates.knowledgeBaseStats.lastTrainingDate),
      };
    }
    
    await updateDoc(projectRef, updateData);
  } catch (error) {
    console.error('Error updating project:', error);
    throw new Error('Failed to update project');
  }
};

export const deleteProject = async (projectId: string): Promise<void> => {
  try {
    // Delete all chunks first
    await deleteProjectChunks(projectId);
    
    // Then delete the project
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    await deleteDoc(projectRef);
  } catch (error) {
    console.error('Error deleting project:', error);
    throw new Error('Failed to delete project');
  }
};

// Knowledge chunk operations
export const createKnowledgeChunk = async (chunkData: Omit<KnowledgeChunk, 'id'>): Promise<string> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, chunkData.projectId, COLLECTIONS.CHUNKS);
    const chunkWithTimestamp = {
      ...chunkData,
      createdAt: Timestamp.fromDate(chunkData.createdAt),
    };
    const docRef = await addDoc(chunksRef, chunkWithTimestamp);
    return docRef.id;
  } catch (error) {
    console.error('Error creating knowledge chunk:', error);
    throw new Error('Failed to create knowledge chunk');
  }
};

export const createKnowledgeChunksBatch = async (chunks: Omit<KnowledgeChunk, 'id'>[]): Promise<void> => {
  try {
    if (chunks.length === 0) return;
    
    const batch = writeBatch(db);
    const projectId = chunks[0].projectId;
    
    chunks.forEach(chunkData => {
      const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
      const chunkRef = doc(chunksRef);
      const chunkWithTimestamp = {
        ...chunkData,
        createdAt: Timestamp.fromDate(chunkData.createdAt),
      };
      batch.set(chunkRef, chunkWithTimestamp);
    });
    
    await batch.commit();
  } catch (error) {
    console.error('Error creating knowledge chunks batch:', error);
    throw new Error('Failed to create knowledge chunks');
  }
};

export const getProjectChunks = async (projectId: string): Promise<KnowledgeChunk[]> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
    const querySnapshot = await getDocs(chunksRef);
    
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as KnowledgeChunk
    );
  } catch (error) {
    console.error('Error getting project chunks:', error);
    throw new Error('Failed to get project chunks');
  }
};

export const deleteProjectChunks = async (projectId: string): Promise<void> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
    const querySnapshot = await getDocs(chunksRef);
    
    const batch = writeBatch(db);
    querySnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
  } catch (error) {
    console.error('Error deleting project chunks:', error);
    throw new Error('Failed to delete project chunks');
  }
};

export const getChunkCount = async (projectId: string): Promise<number> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
    const querySnapshot = await getDocs(chunksRef);
    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting chunk count:', error);
    return 0;
  }
};
