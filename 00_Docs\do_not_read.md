when I login my account I see a blank page with rounded loading icon in center. nothing happens. and I see this error in console: "(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation

react-dom_client.js?v=433f0db0:18249 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
:5174/favicon.ico:1 
 Failed to load resource: the server responded with a status of 404 (Not Found)
firestoreService.ts:67 Error getting user: FirebaseError: Missing or insufficient permissions.
userService.ts:54 Error ensuring user document: Error: Failed to get user
    at getUser (firestoreService.ts:68:11)
    at async ensureUserDocument (userService.ts:8:16)
    at async Object.next (AuthContext.tsx:71:27)
AuthContext.tsx:74 Error managing user document: Error: Failed to manage user document
    at ensureUserDocument (userService.ts:55:11)
    at async Object.next (AuthContext.tsx:71:27)
firebase_firestore.js?v=433f0db0:2147 
 POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Listen/chann…bYgRD4&SID=STcwEAW7OAohf_KKjudLvA&RID=68845&TYPE=terminate&zx=xtaettsptm2k 400 (Bad Request)"

add credentials to a .env file in root
 here is the Gemini API key: AIzaSyCm82D7QsXiqXUbK_3gqsyJYKo5uO5UqpU
 Firebase Credentials:
 apiKey: "AIzaSyCqUdJ0iGXanGhokCtPdTxvdDnY1ynQPsE",
  authDomain: "ecomqna.firebaseapp.com",
  projectId: "ecomqna",
  storageBucket: "ecomqna.firebasestorage.app",
  messagingSenderId: "448731108888",
  appId: "1:448731108888:web:c2778842a1f283a3cd156d",
  measurementId: "G-J3FKPP5C94"

analyze the full codebase and fix the issues smartly
-----------------------------------
I have changed the Firestore security rules manually in the Firebase console. now I am getting this error in console after login: "firestoreService.ts:51 Error creating user: FirebaseError: Function setDoc() called with invalid data. Unsupported field value: undefined (found in field displayName in document users/Skv9TwLxLJa51AFsxCpLeNW5IQ82)

userService.ts:54 Error ensuring user document: Error: Failed to create user: Function setDoc() called with invalid data. Unsupported field value: undefined (found in field displayName in document users/Skv9TwLxLJa51AFsxCpLeNW5IQ82)
    at createUser (firestoreService.ts:61:13)
    at ensureUserDocument (userService.ts:20:13)
    at async Object.next (AuthContext.tsx:74:27)
AuthContext.tsx:77 Error managing user document: Error: Failed to manage user document
    at ensureUserDocument (userService.ts:55:11)
    at async Object.next (AuthContext.tsx:74:27)"
communicate with firebase ecomqna project with CLI smartly to do what you