import React, { createContext, useState, useEffect, ReactNode } from 'react';
import {
  Auth,
  User,
  onAuthStateChanged,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail
} from 'firebase/auth';
import { auth } from '../config/firebase';
import { ensureUserDocument } from '../services/userService';
import { User as AppUser } from '../types';

type Theme = 'light' | 'dark';

interface AuthContextType {
  currentUser: User | null;
  appUser: AppUser | null;
  loading: boolean;
  signup: (email: string, password: string) => Promise<any>;
  signin: (email: string, password: string) => Promise<any>;
  signout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  theme: Theme;
  toggleTheme: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [appUser, setAppUser] = useState<AppUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [theme, setTheme] = useState<Theme>('dark'); // Default to dark

  // Theme management logic
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as Theme | null;
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
    setTheme(initialTheme);
  }, []);

  useEffect(() => {
    const root = window.document.documentElement;
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    localStorage.setItem('theme', theme);
  }, [theme]);
  
  const toggleTheme = () => {
    setTheme((prevTheme) => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  // Authentication logic
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);

      if (user) {
        try {
          // Add a small delay to ensure auth token is fully propagated
          await new Promise(resolve => setTimeout(resolve, 100));

          // Ensure user document exists in Firestore
          const userDoc = await ensureUserDocument(user);
          setAppUser(userDoc);
        } catch (error) {
          console.error('Error managing user document:', error);

          // Check if it's a permissions error
          if (error instanceof Error && error.message.includes('permissions')) {
            console.warn('Firestore permissions error. User is authenticated but cannot access Firestore. Please check Firestore security rules.');

            // Create a minimal user object from Firebase Auth data
            const fallbackUser: AppUser = {
              id: user.uid,
              email: user.email || '',
              displayName: user.displayName || undefined,
              createdAt: new Date(),
              updatedAt: new Date(),
            };
            setAppUser(fallbackUser);
          } else {
            setAppUser(null);
          }
        }
      } else {
        setAppUser(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);
  
  const signup = (email: string, password: string) => {
    return createUserWithEmailAndPassword(auth, email, password);
  };

  const signin = (email: string, password: string) => {
    return signInWithEmailAndPassword(auth, email, password);
  };

  const signout = () => {
    return signOut(auth);
  };

  const resetPassword = (email: string) => {
    return sendPasswordResetEmail(auth, email);
  };

  const value = {
    currentUser,
    appUser,
    loading,
    signup,
    signin,
    signout,
    resetPassword,
    theme,
    toggleTheme,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};